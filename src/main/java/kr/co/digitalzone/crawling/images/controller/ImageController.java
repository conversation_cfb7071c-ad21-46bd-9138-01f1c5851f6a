package kr.co.digitalzone.crawling.images.controller;


import jakarta.validation.Valid;
import kr.co.digitalzone.crawling.images.service.ImageService;
//import kr.co.digitalzone.response.ApiResponse;
//import kr.co.digitalzone.response.ApiResponseEnum;
import kr.co.digitalzone.dto.ImageCollectionReqDto;
import kr.co.digitalzone.dto.ImageCollectionResultDto;
import kr.co.digitalzone.user.common.dto.ResponseDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/crawling")
public class ImageController {

    @Autowired
    private ImageService imageService;

//    @PostMapping("/images")
//    public String collectImages(@RequestParam String keyword, @RequestParam int collect_count) {
//        return imageService.collectImages(keyword, collect_count);
//    }
//    @PostMapping("/images")
//    public ResponseEntity<String> collectImages(@RequestParam String keyword, @RequestParam int collect_count)  {
//        String result = imageService.collectImages(keyword, collect_count);
//        return ResponseEntity.ok(result);
//    }
//    @GetMapping("/progress")
//    public ResponseEntity<Integer> getProgress() {
//        int collectedCount = imageService.getCollectedImagesCount();
//        return ResponseEntity.ok(collectedCount);
//}
//    @GetMapping("/progress")
//    public Map<String, Object> getProgress() {
//        return imageService.getProgress();
//    }
//    @GetMapping("/detail")
//    public Map<String, Object> getDetail() {
//        return imageService.getDetail();
//    }

    /**
     * 이미지 수집 API
     * 요청: List<ImageCollectionRequestDto>
     * 응답: ResponseDto<List<ImageCollectionResultDto>>
     */
    @PostMapping("/images")
    public ResponseEntity<ResponseDto<List<ImageCollectionResultDto>>> collectImages(
            @Valid @RequestBody List<ImageCollectionReqDto> requests) {

        try {
            List<ImageCollectionResultDto> results = imageService.collectImages(requests);

            // 결과 분석
            long collectingCount = results.stream()
                    .filter(result -> "수집 중".equals(result.getStatus()))
                    .count();

            long failureCount = results.stream()
                    .filter(result -> "실패".equals(result.getStatus()) || "오류".equals(result.getStatus()))
                    .count();

            // 응답 메시지와 상태 코드 결정
            ResponseDto<List<ImageCollectionResultDto>> response;
            HttpStatus httpStatus;

            if (failureCount == 0 && collectingCount > 0) {
                // 모든 요청이 수집 중인 경우 (정상 시작)
                response = new ResponseDto<>(200, "이미지 수집 성공", results);
                httpStatus = HttpStatus.OK;
            } else if (collectingCount == 0) {
                // 모든 요청이 실패한 경우
                response = new ResponseDto<>(400, "이미지 수집 실패", results);
                httpStatus = HttpStatus.BAD_REQUEST;
            } else {
                // 일부 성공, 일부 실패한 경우
                String message = String.format("이미지 수집 부분 완료 (수집 중: %d, 실패: %d)",
                        collectingCount, failureCount);
                response = new ResponseDto<>(206, message, results);
                httpStatus = HttpStatus.PARTIAL_CONTENT;
            }

            return new ResponseEntity<>(response, httpStatus);

        } catch (Exception e) {
            ResponseDto<List<ImageCollectionResultDto>> errorResponse =
                    new ResponseDto<>(500, "이미지 수집 실패: " + e.getMessage(), null);

            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * 실시간 데이터 수집 현황 조회 (10초 간격 폴링용)
     * 대시보드에서 실시간 모니터링을 위해 사용
     * @return 실시간 수집 현황 데이터
     */
    @GetMapping("/status")
    public Map<String, Object> getCrawlingStatus() {
        return imageService.getCrawlingStatus();
    }

    /**
     * 특정 job_id의 실시간 데이터 수집 현황 조회 (10초 간격 폴링용)
     * @param jobId 조회할 작업 ID
     * @return 특정 작업의 실시간 수집 현황
     */
    @PostMapping("/countJobId")
    public Map<String, Object> getCrawlingCount(@RequestParam String jobId) {
        return imageService.getCrawlingCount(jobId);
    }

    /**
     * 전체 데이터 수집 카운트 현황 조회
     * 파이 그래프용
     * @return 전체 수집 현황
     */
    @GetMapping("/count")
    public Map<String, Object> getCrawlingCount() {
        return imageService.getCrawlingCount();
    }

//    /**
//     * 실시간 진행률 조회 API (간단 버전)
//     * 클라이언트에서 10초마다 호출하여 진행률만 확인
//     * @param jobId 조회할 작업 ID
//     * @return 간단한 진행률 정보
//     */
//    @GetMapping("/progress/{jobId}")
//    public ResponseEntity<Map<String, Object>> getProgress(@PathVariable String jobId) {
//        try {
//            Map<String, Object> result = imageService.getCrawlingCount(jobId);
//
//            // 간단한 진행률 정보만 추출
//            Map<String, Object> progressInfo = new HashMap<>();
//            progressInfo.put("job_id", jobId);
//            progressInfo.put("timestamp", System.currentTimeMillis());
//
//            if (result.get("code").equals(200)) {
//                List<Map<String, Object>> data = (List<Map<String, Object>>) result.get("data");
//
//                int totalTarget = 0;
//                long totalCollected = 0;
//                boolean allCompleted = true;
//                boolean anyFailed = false;
//
//                for (Map<String, Object> item : data) {
//                    totalTarget += (Integer) item.get("target_count");
//                    totalCollected += (Long) item.get("collected_count");
//
//                    String status = (String) item.get("status");
//                    if (!"완료".equals(status)) {
//                        allCompleted = false;
//                    }
//                    if ("실패".equals(status)) {
//                        anyFailed = true;
//                    }
//                }
//
//                double overallProgress = totalTarget > 0 ? (double) totalCollected / totalTarget * 100 : 0;
//
//                progressInfo.put("total_target", totalTarget);
//                progressInfo.put("total_collected", totalCollected);
//                progressInfo.put("overall_progress", Math.round(overallProgress * 100.0) / 100.0);
//                progressInfo.put("is_completed", allCompleted);
//                progressInfo.put("has_failed", anyFailed);
//                progressInfo.put("details", data);
//
//                return ResponseEntity.ok(progressInfo);
//            } else {
//                progressInfo.put("error", result.get("message"));
//                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(progressInfo);
//            }
//
//        } catch (Exception e) {
//            Map<String, Object> errorInfo = new HashMap<>();
//            errorInfo.put("job_id", jobId);
//            errorInfo.put("error", "진행률 조회 실패: " + e.getMessage());
//            errorInfo.put("timestamp", System.currentTimeMillis());
//
//            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorInfo);
//        }
//    }

    /**
     * 키워드별 데이터 보유 현황 조회
     *  막대그래프
     * @return
     */
    @GetMapping("/keywordCount")
    public Map<String, Object> getKeywordCount() {
        return imageService.getKeywordCount();
    }

    /**
     * 특정 jobId 데이터 삭제
     * @param jobId 삭제할 작업 ID
     * @return 삭제 결과
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteCrawlingData(@RequestParam String jobId) {
        try {
            Map<String, Object> result = imageService.deleteCrawlingData(jobId);

            int code = (Integer) result.get("code");
            HttpStatus httpStatus;

            switch (code) {
                case 200:
                    httpStatus = HttpStatus.OK;
                    break;
                case 404:
                    httpStatus = HttpStatus.NOT_FOUND;
                    break;
                case 500:
                default:
                    httpStatus = HttpStatus.INTERNAL_SERVER_ERROR;
                    break;
            }

            return new ResponseEntity<>(result, httpStatus);

        } catch (Exception e) {
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("code", 500);
            errorResponse.put("message", "데이터 삭제 실패: " + e.getMessage());
            errorResponse.put("jobId", jobId);

            return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
}



