package kr.co.digitalzone.repository;

import kr.co.digitalzone.dto.CrawlingCountDto;
import kr.co.digitalzone.entity.TargetData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface TargetDataRepository extends JpaRepository<TargetData, Long> {

    /**
     * 모든 타겟 데이터 조회 (최신순)
     */
    @Query("SELECT new kr.co.digitalzone.dto.CrawlingCountDto(td.source, td.keyword, td.targetCount) " +
            "FROM TargetData td " +
            "ORDER BY td.startDate DESC")
    List<CrawlingCountDto> findAllTargetData();

    /**
     * 특정 job_id로 타겟 데이터 조회
     * Spring Data JPA 메서드 네이밍 규칙에 의해 자동으로 구현됨
     */
    List<TargetData> findByJobId(String jobId);

    /**
     * 특정 job_id의 타겟 데이터 삭제
     */
    @Modifying
    @Query("DELETE FROM TargetData td WHERE td.jobId = :jobId")
    void deleteByJobId(@Param("jobId") String jobId);

    /**
     * 모든 타겟 데이터 조회 (최신순) - 실시간 모니터링용
     */
    @Query("SELECT td FROM TargetData td ORDER BY td.startDate DESC")
    List<TargetData> findAllOrderByStartDateDesc();

//    /**
//     * 진행 중인 작업만 조회 (실시간 모니터링용)
//     */
//    @Query("SELECT td FROM TargetData td WHERE td.stats IN ('수집 중', '대기 중') ORDER BY td.startDate DESC")
//    List<TargetData> findActiveJobs();
//
//    /**
//     * 특정 상태의 작업 조회
//     */
//    @Query("SELECT td FROM TargetData td WHERE td.stats = :status ORDER BY td.startDate DESC")
//    List<TargetData> findByStatus(@Param("status") String status);

}